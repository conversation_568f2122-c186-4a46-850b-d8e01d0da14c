/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { ValueHistory } from '@g17eco/types/universalTrackerValue';
import { ReportData } from '../types/reportData';
import { TableColumnType, UniversalTrackerPlain, UtrValueType } from '../types/universalTracker';
import UniversalTracker from '@models/UniversalTracker';
import { ScopeQuestionOptionalValue } from '@g17eco/types/surveyScope';

export const questionObj2: ReportData = {
  '_id': '5f884eccb20515c62cb0c949',
  'effectiveDate': '2019-02-28T23:59:59.999Z',
  status: 'verified',
  'history': [
    {
      _id: '12312312312312',
      'evidence': [],
      'action': 'created',
      'userId': '5e9880d9cf080551e107a89b',
      'date': '2020-10-15T13:29:48.813Z',
      value: undefined,
    },
    {
      _id: '122222222312312312312',
      'evidence': [
        '5fb4048b871be176bfc85610'
      ],
      'action': 'updated',
      'userId': '5e9880d9cf080551e107a89b',
      'note': 'First comments',
      value: undefined,
      'valueData': {
        'input': {
          'table': [
            [
              {
                'code': 'country',
                'value': 'al'
              },
              {
                'code': 'human_rights_number',
                'value': '4'
              },
              {
                'code': 'human_rights_percentage',
                'value': '43'
              }
            ],
            [
              {
                'code': 'country',
                'value': 'ad'
              },
              {
                'code': 'human_rights_number',
                'value': '2'
              },
              {
                'code': 'human_rights_percentage',
                'value': '67'
              }
            ],
            [
              {
                'code': 'country',
                'value': 'ai'
              },
              {
                'code': 'human_rights_number',
                'value': '7'
              },
              {
                'code': 'human_rights_percentage',
                'value': '57'
              }
            ]
          ],
          'unit': ''
        },
        'table': [
          [
            {
              'code': 'country',
              'value': 'al'
            },
            {
              'code': 'human_rights_number',
              'value': '4'
            },
            {
              'code': 'human_rights_percentage',
              'value': '43'
            }
          ],
          [
            {
              'code': 'country',
              'value': 'ad'
            },
            {
              'code': 'human_rights_number',
              'value': '2'
            },
            {
              'code': 'human_rights_percentage',
              'value': '67'
            }
          ],
          [
            {
              'code': 'country',
              'value': 'ai'
            },
            {
              'code': 'human_rights_number',
              'value': '7'
            },
            {
              'code': 'human_rights_percentage',
              'value': '57'
            }
          ]
        ]
      },
      'location': {
        'ip': '127.0.0.1',
      },
      'valueType': UtrValueType.Table,
      'date': '2020-11-17T17:12:43.894Z'
    },
    {
      _id: '22222222312312',
      'evidence': [],
      value: undefined,
      'action': 'verified',
      'userId': '5e9880d9cf080551e107a89b',
      'note': '',
      'location': {
        'ip': '127.0.0.1',
      },
      'valueType': UtrValueType.Table,
      'date': '2020-11-17T17:12:43.900Z'
    }
  ],
  'valueData': {
    'input': {
      'table': [
        [
          {
            'code': 'country',
            'value': 'al'
          },
          {
            'code': 'human_rights_number',
            'value': '4'
          },
          {
            'code': 'human_rights_percentage',
            'value': '43'
          }
        ],
        [
          {
            'code': 'country',
            'value': 'ad'
          },
          {
            'code': 'human_rights_number',
            'value': '2'
          },
          {
            'code': 'human_rights_percentage',
            'value': '67'
          }
        ],
        [
          {
            'code': 'country',
            'value': 'ai'
          },
          {
            'code': 'human_rights_number',
            'value': '7'
          },
          {
            'code': 'human_rights_percentage',
            'value': '57'
          }
        ]
      ],
      'unit': ''
    },
    'table': [
      [
        {
          'code': 'country',
          'value': 'al'
        },
        {
          'code': 'human_rights_number',
          'value': '4'
        },
        {
          'code': 'human_rights_percentage',
          'value': '43'
        }
      ],
      [
        {
          'code': 'country',
          'value': 'ad'
        },
        {
          'code': 'human_rights_number',
          'value': '2'
        },
        {
          'code': 'human_rights_percentage',
          'value': '67'
        }
      ],
      [
        {
          'code': 'country',
          'value': 'ai'
        },
        {
          'code': 'human_rights_number',
          'value': '7'
        },
        {
          'code': 'human_rights_percentage',
          'value': '57'
        }
      ]
    ]
  },
  'universalTracker': {
    '_id': '5e735d7d1f2c7d3484ed6d6f',
    'targetDirection': 'increase',
    'valueListOrdered': [],
    'valueListTargets': [],
    'code': 'gri/2020/412-1/a',
    'name': 'Total Number of Operations',
    'valueLabel': 'Total number and percentage of operations that have been subject to human rights reviews or human rights impact assessments, by country.',
    'instructions': 'Information reported for this disclosure can show the extent to which an organization considers human rights when making decisions on its locations of operations. It can also provide information to assess the organization’s potential to be associated with, or to be considered complicit in, human rights abuse.',
    'evidenceInstructions': '- HR report\n- Human Right Policy\n- Operations Audit\n\n',
    'instructionsLink': 'https://www.globalreporting.org/standards/gri-standards-download-center/',
    'type': 'gri',
    'typeCode': '412-1/a',
    'valueType': 'table',
    'unit': '',
    'tags': {
      'tcfd': [],
      'pic': [
        'poverty',
        'inequality'
      ],
      'ese': [],
      'ungc': [
        'ungc-one',
        'ungc-two'
      ],
      'wef': []
    },
    'created': '2020-03-19T11:54:37.887Z',
    'alternatives': {
      'gri': {
        'typeCode': '412-1/a',
        'name': 'Total number of operations',
        'valueLabel': 'Total number and percentage of operations that have been subject to human rights reviews or human rights impact assessments, by country.',
        'instructions': '',
        'evidenceInstructions': '- HR report\n- Human Right Policy\n- Operations Audit\n\n',
        'instructionsLink': 'https://www.globalreporting.org/standards/media/1027/gri-412-human-rights-assessment-2016.pdf#page=7'
      }
    },
    'valueValidation': {
      'table': {
        'columns': [
          {
            'code': 'human_rights_number',
            'name': 'Total number of operations that have been subject to human rights reviews or human rights impact assessments?',
            'type': TableColumnType.Number,
            'shortName': 'Total Number',
            'options': []
          },
          {
            'code': 'human_rights_percentage',
            'name': 'Percentage of operations that have been subject to human rights reviews or human rights impact assessments?',
            'type': TableColumnType.Number,
            'shortName': 'Percentage',
            'validation': {
              'required': false,
              'allowCustomOptions': false,
              'min': 0,
              'max': 100
            },
            'options': []
          },
          {
            'code': 'country',
            'name': 'Country',
            'type': TableColumnType.Text,
            'instructions': 'Please select a country from the list.',
            'shortName': 'Country',
            'listId': '5e81d4705922c919f758da15',
            'options': []
          }
        ]
      }
    }
  }
}

export const defaultUniversalTrackerFields: Pick<
  UniversalTrackerPlain,
  '_id' | 'code' | 'created' | 'type' | 'typeCode' | 'valueLabel' | 'valueType' | 'name'
> = {
  _id: '',
  code: '',
  created: '',
  name: '',
  type: '',
  typeCode: '',
  valueLabel: '',
  valueType: '',
};

export const generateValueHistory = (data: Partial<ValueHistory> = {}): ValueHistory => {
  return {
    action: 'verified',
    unit: '',
    userId: '658d3953f2f33f3c31ecfc5d',
    value: undefined,
    valueData: {},
    assuranceFields: [],
    evidence: [],
    _id: '660e8588f05223fd889dc1dc',
    date: '2024-04-04T10:48:40.645Z',
    ...data,
  };
}

// Helper function to create mock ScopeQuestionOptionalValue
export const createScopeQuestion = (code: string, name: string = `Question ${code}`): ScopeQuestionOptionalValue => {
  const utr = new UniversalTracker({
    ...defaultUniversalTrackerFields,
    _id: `utr-${code}`,
    code: code,
    name: name,
    typeCode: code.toUpperCase(),
  });

  return {
    name: name,
    universalTracker: utr,
    frameworkCode: utr.getFrameworkCode(),
    valueType: utr.getValueType(),
    valueValidation: utr.getValueValidation(),
    shortPrefix: utr.getShortPrefix(),
    sortTitle: name,
  };
};
