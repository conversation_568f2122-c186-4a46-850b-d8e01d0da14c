/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CustomScope, InitiativePlain } from '../types/initiative';
import { ScopeQuestionOptionalValue, UniversalTrackerValuePlain } from '../types/surveyScope';
import { StakeholderGroup } from './stakeholderGroup';
import { DataPeriods } from '../types/universalTracker';
import { AggregatedSurveyFilters, BlueprintContributions, QuestionGroup, SurveyType } from '../types/survey';
import { MetricGroup } from '../types/metricGroup';
import { SerializedEditorState } from 'lexical';
import { BulkActionUtrMin } from '@components/survey-question-list/partials/BulkActionToolbar';

export enum ScopeGroups {
  Sdg = 'sdg',
  Materiality = 'materiality',
  Standards = 'standards',
  Frameworks = 'frameworks',
  Custom = 'custom'
}

export interface Scope {
  [ScopeGroups.Sdg]: string[],
  [ScopeGroups.Materiality]: string[],
  [ScopeGroups.Standards]: string[],
  [ScopeGroups.Frameworks]: string[],
  [ScopeGroups.Custom]: string[],
}

export interface DelegationScopeChildGroup extends StakeholderGroup {
  code: string;
  inScope?: boolean;
  isPartial?: boolean;
  children?: DelegationScopeChildGroup[];
}

export interface DelegationScopeUsersGroup extends StakeholderGroup {
  inScope?: boolean;
  isPartial?: boolean;
  children?: DelegationScopeChildGroup[];
}

interface DelegationScopeUsers {
  [key: string]: DelegationScopeUsersGroup
}

export interface DelegationScope {
  [ScopeGroups.Sdg]: DelegationScopeUsers;
  [ScopeGroups.Materiality]: DelegationScopeUsers;
  [ScopeGroups.Standards]: DelegationScopeUsers;
  [ScopeGroups.Frameworks]: DelegationScopeUsers;
  [ScopeGroups.Custom]: DelegationScopeUsers;
}

export interface Roles<T = string> {
  viewer: T[];
  admin: T[];
}

export interface GroupData {
  colour?: string;
  link?: string;
  icon?: string;
  alternativeCode?: string;
  preferredAltCodes?: string[];
  isInherited?: boolean;
}

export enum GroupType {
  Custom = 'custom',
  Group = 'group',
  Static = 'static',
}

export interface UtrGroupConfig {
  utrCodes: string[];
  groupName: string;
  groupData: GroupData;
  type?: GroupType;
  groupId?: string;
  groupDate?: string;
}

export interface NonStaticGroupConfig extends UtrGroupConfig {
  groupId: string;
  type: GroupType;
}

export interface Form {
  utrGroupConfig: UtrGroupConfig;
}

export interface AdditionalConfig {
  compositeConfig: string;
}

export interface UnitConfig {
  area: string;
  length: string;
  time: string;
  mass: string;
  volume: string;
  energy: string;
  currency: string;
  co2Emissions: string;
  numberScale: string;
  partsPer: string;
}

// specified also on api unitTypes.ts
export const blueprintDefaultUnitConfig: UnitConfig = {
  area: 'km2',
  length: 'km',
  time: 'h',
  mass: 'mt',
  volume: 'm3',
  energy: 'MWh',
  currency: 'USD',
  co2Emissions: 'tons/CO2e',
  numberScale: 'millions',
  partsPer: 'ppm',
};

export const blueprintDefaultUnitConfigSgx: UnitConfig = {
  ...blueprintDefaultUnitConfig,
  currency: 'SGD'
}

export interface Blueprint {
  references: any[];
  forms: Form[];
  additionalConfigs: AdditionalConfig[];
  code: string;
  name: string;
  unitConfig: UnitConfig;
  customGroups?: NonStaticGroupConfig[];
}

interface ScopeUpdate {
  _id?: string;
  type: GroupType;
  name: string;
  added: string[],
  removed: string[],
}

export type SurveyModelMinimalUtrv = Pick<
  UniversalTrackerValuePlain,
  | '_id'
  | 'universalTrackerId'
  | 'initiativeId'
  | 'valueData'
  | 'value'
  | 'status'
  | 'stakeholders'
  | 'assuranceStatus'
  | 'lastUpdated'
  | 'evidenceRequired'
  | 'noteRequired'
  | 'verificationRequired'
  | 'isPrivate'
  | 'effectiveDate'
  | 'compositeData'
  | 'notes'
  | 'sourceItems'
> & { utr?: BulkActionUtrMin };

type SurveyModelMinimalInitiative = Pick<
  InitiativePlain,
  '_id' | 'name' | 'customer' | 'referrals' | 'permissionGroup' | 'type' | 'tags' | 'materialityMap'
>;

export interface ScheduledDate {
  idempotencyKey?: string;
  date: string;
}

export interface FormScheduledDate extends ScheduledDate {
  status: 'updated' | 'original';
}

export interface ScopeWheelPreferences {
  scopeCode: string;
  name: string;
  visible: boolean;
}

interface DisplayPreferences {
  scopeWheels?: ScopeWheelPreferences[];
}

export interface SurveyModelMinData {
  _id: string;
  code: string;
  initiativeId: string;
  effectiveDate: string;
  completedDate?: string;
  utrvType: string;
  evidenceRequired: boolean;
  verificationRequired: boolean;
  noteRequired?: boolean;
  noteInstructions?: string;
  noteInstructionsEditorState?: SerializedEditorState;
  isPrivate: boolean;
  stakeholders: StakeholderGroup;
  name?: string;
  type?: SurveyType;
  sourceName: string;
  scope?: Scope;
  scopeConfig?: CustomScope[];
  delegationScope?: DelegationScope;
  roles?: Roles;
  config?: Pick<Blueprint, 'unitConfig' | 'customGroups' | 'forms'>;
  unitConfig?: UnitConfig;
  period?: DataPeriods;
  created: string;
  initiatives: SurveyModelMinimalInitiative[];
  scopeUpdates?: ScopeUpdate[];
  ignoredDate: string;
  deadlineDate?: string;
  scheduledDates?: ScheduledDate[];
  creatorId?: string;
  utrSortingConfigMap?: UtrSortingConfigMap;
}

export type NoteBaseTypes = Pick<SurveyModelMinData,
  | 'noteInstructions'
  | 'noteInstructionsEditorState'
  | 'noteRequired'
>

export interface Company {
  permissionGroup?: string;
}

export interface UtrSortingConfig {
  code: string;
  order?: string[];
  subgroups?: {
    code: string;
    order?: string[];
    subgroups?: UtrSortingConfig['subgroups'];
  }[];
}

interface UtrSortingConfigMap {
  [key: string]: UtrSortingConfig;
}

export interface SurveyActionData extends SurveyModelMinData {
  fragmentUniversalTrackerValues: SurveyModelMinimalUtrv[];
  questionGroups: QuestionGroup[];
  customMetricGroups: MetricGroup[];
  contributions: BlueprintContributions;
  displayPreferences?: DisplayPreferences;
  aggregatedDate?: string;
  aggregatedSurveys?: Pick<SurveyModelMinData, '_id' | 'initiativeId' | 'effectiveDate' | 'type' | 'period'>[];
  filters?: AggregatedSurveyFilters;
}

export type SurveyGroupsData<T extends ScopeQuestionOptionalValue = ScopeQuestionOptionalValue> = Pick<SurveyActionData,
  'scope' |
  'customMetricGroups' |
  'contributions' |
  'initiativeId' |
  'initiatives' |
  'utrSortingConfigMap'
  > & {
  questionGroups: QuestionGroup<T>[];
  fragmentUniversalTrackerValues?: SurveyActionData['fragmentUniversalTrackerValues']
};


export const isSurveyActionData = (d: Partial<SurveyActionData>): d is SurveyActionData => Boolean(d._id)
