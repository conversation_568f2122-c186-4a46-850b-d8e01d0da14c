/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import React from 'react';
import { ViewValues } from '../survey-overview-sidebar/viewOptions';
import { SurveyModelMinData } from '../../model/surveyData';
import SurveyQuestionList from '../survey-question-list/survey-question-list';
import { Breadcrumb, ScopeQuestionOptionalValue } from '../../types/surveyScope';
import { findMatchingConfig, sortItemsByTypeCode, sortItemsByUtrSortingOrder } from '../../utils/sort';
import { convertToFilters } from '../survey/utils/useScopeFilters';
import { standards } from '@g17eco/core';

interface ScopeQuestionListProps {
  surveyData: SurveyModelMinData,
  questions: ScopeQuestionOptionalValue[],
  view: ViewValues;
  breadcrumbs: Breadcrumb[];
}

export const shouldShowScopeQuestions = (cardGroup: ViewValues) => [
  ViewValues.StandardsQuestionList,
].includes(cardGroup);

const viewWithStandards = [ViewValues.Standards, ViewValues.QuestionPacks];

export const ScopeQuestionList = (props: ScopeQuestionListProps) => {
  const { surveyData, questions, view, breadcrumbs } = props;

  const filters = convertToFilters(view, breadcrumbs);
  const filter = filters.find(f => viewWithStandards.includes(f.type as ViewValues));
  // Ensure filter value is valid altCode, excludes frameworks from QuestionPack
  const alternativeCode = filter?.value && standards[filter.value] ? filter.value : '';

  // First item of filters should be the group tag
  const groupTag = filters[0].value;
  // Last item of filters should be the current group view from the breadcrumbs
  const currentView = filters[filters.length - 1].value;
  const order =
    groupTag && currentView
      ? findMatchingConfig(surveyData.utrSortingConfigMap?.[groupTag], currentView)?.order
      : undefined;
  const sortedQuestions = order
    ? sortItemsByUtrSortingOrder(questions, order)
    : sortItemsByTypeCode(questions, alternativeCode);

  return <SurveyQuestionList
    surveyGroups={[{
      groupName: '',
      list: sortedQuestions
    }]}
    alternativeCode={alternativeCode}
    surveyId={surveyData._id}
    initiativeId={surveyData.initiativeId}
  />
}
